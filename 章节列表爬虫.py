import requests
from bs4 import BeautifulSoup
import time
import random

def get_novel_content(url):
    """
    获取小说网页内容
    
    Args:
        url (str): 目标网址
        
    Returns:
        str: 网页内容
    """
    # 设置请求头，模拟浏览器访问
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }
    
    try:
        # 发送GET请求
        print(f"正在请求网址: {url}")
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()  # 检查请求是否成功
        
        # 设置编码
        response.encoding = 'utf-8'
        
        print(f"请求成功，状态码: {response.status_code}")
        
        return response.text
        
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return None

def extract_chapters(html_content):
    """
    提取章节信息和URL
    
    Args:
        html_content (str): HTML内容
        
    Returns:
        dict: 包含章节信息的字典
    """
    if not html_content:
        return None
    
    soup = BeautifulSoup(html_content, 'html.parser')
    
    result = {
        'novel_title': '',
        'total_chapters': 0,
        'chapters': [],
        'navigation': {}
    }
    
    # 提取小说标题
    title_element = soup.find('div', class_='read')
    if title_element:
        # 查找包含标题的文本
        title_text = title_element.find_previous_sibling('div')
        if title_text:
            # 提取标题文本，去除HTML标签
            title_text = title_text.get_text().strip()
            # 提取标题（通常在【】之间）
            if '【' in title_text and '】' in title_text:
                start = title_text.find('【') + 1
                end = title_text.find('】')
                if start > 0 and end > start:
                    result['novel_title'] = title_text[start:end]
    
    # 提取总章节数
    if title_element:
        title_text = title_element.find_previous_sibling('div')
        if title_text:
            text = title_text.get_text()
            if '共' in text and '章' in text:
                try:
                    # 提取数字
                    import re
                    numbers = re.findall(r'\d+', text)
                    if numbers:
                        result['total_chapters'] = int(numbers[-1])  # 取最后一个数字
                except:
                    pass
    
    # 提取章节列表
    chapter_list = soup.find('ul', class_='read')
    if chapter_list:
        chapter_items = chapter_list.find_all('li')
        for item in chapter_items:
            chapter_link = item.find('a')
            if chapter_link:
                chapter_id = item.get('chapter-id', '')
                chapter_title = chapter_link.get_text().strip()
                chapter_url = chapter_link.get('href', '')
                
                # 构建完整URL
                if chapter_url.startswith('/'):
                    chapter_url = 'https://m.shuhaige.net' + chapter_url
                
                result['chapters'].append({
                    'id': chapter_id,
                    'title': chapter_title,
                    'url': chapter_url
                })
    
    # 提取导航信息
    nav_links = soup.find_all('a')
    for link in nav_links:
        href = link.get('href', '')
        text = link.get_text().strip()
        
        if '下一页' in text:
            if href.startswith('/'):
                href = 'https://m.shuhaige.net' + href
            result['navigation']['next_page'] = href
        elif '尾页' in text:
            if href.startswith('/'):
                href = 'https://m.shuhaige.net' + href
            result['navigation']['last_page'] = href
        elif '首页' in text or '首页' in text:
            if href.startswith('/'):
                href = 'https://m.shuhaige.net' + href
            result['navigation']['first_page'] = href
    
    return result

def save_chapters_to_file(data, filename='章节信息.txt'):
    """
    保存章节信息到文件
    
    Args:
        data (dict): 章节数据
        filename (str): 文件名
    """
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"小说标题: {data['novel_title']}\n")
            f.write(f"总章节数: {data['total_chapters']}\n")
            f.write("=" * 50 + "\n\n")
            
            f.write("章节列表:\n")
            for i, chapter in enumerate(data['chapters'], 1):
                f.write(f"{i:3d}. {chapter['title']}\n")
                f.write(f"     ID: {chapter['id']}\n")
                f.write(f"     URL: {chapter['url']}\n")
                f.write("-" * 30 + "\n")
            
            f.write("\n导航链接:\n")
            for nav_type, url in data['navigation'].items():
                f.write(f"{nav_type}: {url}\n")
        
        print(f"章节信息已保存到文件: {filename}")
        
    except Exception as e:
        print(f"保存文件失败: {e}")

def get_all_chapters(base_url):
    """
    获取所有章节（包括分页）
    
    Args:
        base_url (str): 基础URL
        
    Returns:
        list: 所有章节信息
    """
    all_chapters = []
    current_url = base_url
    page_count = 0
    
    while current_url:
        page_count += 1
        print(f"正在获取第 {page_count} 页: {current_url}")
        
        html_content = get_novel_content(current_url)
        if not html_content:
            print(f"获取第 {page_count} 页失败，停止爬取")
            break
        
        chapter_data = extract_chapters(html_content)
        if chapter_data and chapter_data['chapters']:
            all_chapters.extend(chapter_data['chapters'])
            print(f"第 {page_count} 页找到 {len(chapter_data['chapters'])} 个章节")
        else:
            print(f"第 {page_count} 页未找到章节")
        
        # 获取下一页链接
        soup = BeautifulSoup(html_content, 'html.parser')
        next_link = soup.find('a', text='下一页')
        if next_link:
            next_url = next_link.get('href')
            if next_url.startswith('/'):
                next_url = 'https://m.shuhaige.net' + next_url
            current_url = next_url
            print(f"找到下一页链接: {next_url}")
        else:
            print("未找到下一页链接，爬取完成")
            current_url = None
        
        # 添加延迟，避免请求过快
        time.sleep(random.uniform(1, 2))
    
    print(f"总共爬取了 {page_count} 页，获取到 {len(all_chapters)} 个章节")
    return all_chapters

def main():
    """主函数"""
    url = "https://m.shuhaige.net/365826/"
    
    print("开始爬取小说全部章节信息...")
    print(f"目标网址: {url}")
    print("-" * 50)
    
    # 获取网页内容
    html_content = get_novel_content(url)
    
    if html_content:
        print("正在解析章节信息...")
        
        # 解析章节
        chapter_data = extract_chapters(html_content)
        
        if chapter_data:
            print(f"解析完成!")
            print(f"小说标题: {chapter_data['novel_title']}")
            print(f"总章节数: {chapter_data['total_chapters']}")
            print(f"当前页面章节数: {len(chapter_data['chapters'])}")
            
            # 保存当前页面章节
            save_chapters_to_file(chapter_data, '当前页面章节信息.txt')
            
            # 显示章节列表
            print("\n当前页面章节列表:")
            print("-" * 30)
            for i, chapter in enumerate(chapter_data['chapters'][:10], 1):
                print(f"{i:2d}. {chapter['title']}")
                print(f"    URL: {chapter['url']}")
            
            if len(chapter_data['chapters']) > 10:
                print(f"... 还有 {len(chapter_data['chapters']) - 10} 章")
            
            # 自动获取所有章节
            print("\n开始获取所有页面的章节...")
            all_chapters = get_all_chapters(url)
            
            if all_chapters:
                print(f"\n总共获取到 {len(all_chapters)} 个章节")
                
                # 保存所有章节
                all_data = {
                    'novel_title': chapter_data['novel_title'],
                    'total_chapters': len(all_chapters),
                    'chapters': all_chapters,
                    'navigation': chapter_data['navigation']
                }
                save_chapters_to_file(all_data, '全部章节信息.txt')
                
                # 创建章节URL列表文件
                with open('章节URL列表.txt', 'w', encoding='utf-8') as f:
                    f.write(f"小说: {all_data['novel_title']}\n")
                    f.write(f"总章节数: {len(all_chapters)}\n")
                    f.write("=" * 50 + "\n\n")
                    for i, chapter in enumerate(all_chapters, 1):
                        f.write(f"{i:3d}. {chapter['title']}\n")
                        f.write(f"     {chapter['url']}\n\n")
                
                print("所有章节信息已保存到文件:")
                print("- 全部章节信息.txt")
                print("- 章节URL列表.txt")
                print("- 当前页面章节信息.txt")
            
        else:
            print("解析章节失败")
    else:
        print("获取网页内容失败")

if __name__ == "__main__":
    main() 