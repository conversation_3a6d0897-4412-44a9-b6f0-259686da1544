import requests
from bs4 import BeautifulSoup
import time
import random
import re

def get_novel_content(url):
    """
    获取小说网页内容
    
    Args:
        url (str): 目标网址
        
    Returns:
        str: 网页内容
    """
    # 设置请求头，模拟浏览器访问
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }
    
    try:
        # 发送GET请求
        print(f"正在请求网址: {url}")
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()  # 检查请求是否成功
        
        # 设置编码
        response.encoding = 'utf-8'
        
        print(f"请求成功，状态码: {response.status_code}")
        
        return response.text
        
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return None

def extract_chapter_content(html_content):
    """
    提取章节内容
    
    Args:
        html_content (str): HTML内容
        
    Returns:
        dict: 包含章节标题和内容的字典
    """
    if not html_content:
        return None
    
    soup = BeautifulSoup(html_content, 'html.parser')
    
    result = {
        'title': '',
        'content': '',
        'next_page_url': None
    }
    
    # 提取章节标题
    title_element = soup.find('h1', class_='headline')
    if title_element:
        result['title'] = title_element.get_text().strip()
    
    # 提取章节内容
    content_element = soup.find('div', class_='content')
    if content_element:
        # 获取所有段落
        paragraphs = content_element.find_all('p')
        content_lines = []
        
        for p in paragraphs:
            text = p.get_text().strip()
            if text:  # 只添加非空内容
                content_lines.append(text)
        
        result['content'] = '\n\n'.join(content_lines)
    
    # 检查是否有下一页
    pager_element = soup.find('div', class_='pager')
    if pager_element:
        next_link = pager_element.find('a', string='下一页')
        if next_link:
            next_url = next_link.get('href')
            if next_url.startswith('/'):
                next_url = 'https://m.shuhaige.net' + next_url
            result['next_page_url'] = next_url
    
    return result

def get_full_chapter_content(chapter_url):
    """
    获取完整章节内容（包括分页）
    
    Args:
        chapter_url (str): 章节URL
        
    Returns:
        dict: 包含完整章节内容的字典
    """
    full_content = []
    current_url = chapter_url
    page_num = 1
    chapter_data = None
    
    while current_url:
        print(f"正在获取第 {page_num} 页: {current_url}")
        
        html_content = get_novel_content(current_url)
        if not html_content:
            print(f"获取第 {page_num} 页失败")
            break
        
        chapter_data = extract_chapter_content(html_content)
        if not chapter_data:
            print(f"解析第 {page_num} 页失败")
            break
        
        # 第一页包含标题
        if page_num == 1:
            full_content.append(f"# {chapter_data['title']}")
            full_content.append("")  # 空行
        
        # 添加内容
        if chapter_data['content']:
            full_content.append(chapter_data['content'])
            full_content.append("")  # 空行
        
        # 检查是否有下一页
        if chapter_data['next_page_url']:
            current_url = chapter_data['next_page_url']
            page_num += 1
            # 添加延迟
            time.sleep(random.uniform(0.5, 1))
        else:
            current_url = None
    
    return {
        'title': chapter_data['title'] if chapter_data else '',
        'content': '\n'.join(full_content)
    }

def save_novel_to_txt(chapters_data, filename='小说.txt'):
    """
    保存小说到txt文件
    
    Args:
        chapters_data (list): 章节数据列表
        filename (str): 文件名
    """
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            # 写入小说标题
            if chapters_data:
                first_chapter = chapters_data[0]
                novel_title = first_chapter.get('novel_title', '未知小说')
                f.write(f"《{novel_title}》\n")
                f.write("=" * 50 + "\n\n")
            
            # 写入所有章节
            for i, chapter in enumerate(chapters_data, 1):
                print(f"正在保存第 {i} 章: {chapter['title']}")
                f.write(chapter['content'])
                f.write("\n" + "=" * 50 + "\n\n")
        
        print(f"小说已保存到文件: {filename}")
        
    except Exception as e:
        print(f"保存文件失败: {e}")

def append_chapter_to_txt(chapter_data, filename='小说.txt', is_first=False):
    """
    实时追加章节到txt文件
    
    Args:
        chapter_data (dict): 章节数据
        filename (str): 文件名
        is_first (bool): 是否是第一章节
    """
    try:
        mode = 'w' if is_first else 'a'
        with open(filename, mode, encoding='utf-8') as f:
            if is_first:
                # 写入小说标题
                novel_title = chapter_data.get('novel_title', '未知小说')
                f.write(f"《{novel_title}》\n")
                f.write("=" * 50 + "\n\n")
            
            # 写入章节内容
            f.write(chapter_data['content'])
            f.write("\n" + "=" * 50 + "\n\n")
        
        print(f"✓ 第 {chapter_data['title']} 已保存到文件")
        
    except Exception as e:
        print(f"保存章节失败: {e}")

def crawl_all_chapters(chapter_urls):
    """
    爬取所有章节内容并实时保存
    
    Args:
        chapter_urls (list): 章节URL列表
        
    Returns:
        list: 章节内容列表
    """
    all_chapters = []
    total_chapters = len(chapter_urls)
    
    print(f"开始爬取 {total_chapters} 个章节...")
    print("-" * 50)
    
    for i, chapter_info in enumerate(chapter_urls, 1):
        chapter_title = chapter_info['title']
        chapter_url = chapter_info['url']
        
        print(f"进度: {i}/{total_chapters} - {chapter_title}")
        
        try:
            # 获取章节内容
            chapter_content = get_full_chapter_content(chapter_url)
            
            if chapter_content and chapter_content['content']:
                chapter_data = {
                    'title': chapter_content['title'],
                    'content': chapter_content['content'],
                    'novel_title': '未知小说'  # 可以从第一章节获取
                }
                
                # 实时保存到文件
                is_first = (i == 1)
                append_chapter_to_txt(chapter_data, '小说.txt', is_first)
                
                all_chapters.append(chapter_data)
                print(f"✓ 第 {i} 章爬取成功")
            else:
                print(f"✗ 第 {i} 章爬取失败")
                
        except Exception as e:
            print(f"✗ 第 {i} 章爬取出错: {e}")
            continue
        
        # 添加延迟，避免请求过快
        if i < total_chapters:
            delay = random.uniform(1, 2)
            print(f"等待 {delay:.1f} 秒...")
            time.sleep(delay)
    
    print(f"\n爬取完成！成功爬取 {len(all_chapters)} 个章节")
    return all_chapters

def load_chapter_urls_from_file(filename='章节URL列表.txt'):
    """
    从文件加载章节URL列表
    
    Args:
        filename (str): 文件名
        
    Returns:
        list: 章节信息列表
    """
    chapters = []
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        current_title = None
        
        for line in lines:
            line = line.strip()
            
            if line and not line.startswith('小说:') and not line.startswith('总章节数:') and not line.startswith('='):
                # 匹配章节行 (格式: "  1. 第1章 穿越")
                if re.match(r'^\s*\d+\.\s+', line):
                    # 提取章节标题
                    title_match = re.search(r'^\s*\d+\.\s+(.+)$', line)
                    if title_match:
                        current_title = title_match.group(1).strip()
                # 匹配URL行 (格式: "     https://m.shuhaige.net/365826/126184281.html")
                elif line.startswith('     https://') and current_title:
                    url = line.strip()
                    chapters.append({'title': current_title, 'url': url})
                    current_title = None
                elif line.startswith('https://') and current_title:
                    url = line.strip()
                    chapters.append({'title': current_title, 'url': url})
                    current_title = None
    
    except Exception as e:
        print(f"读取文件失败: {e}")
    
    return chapters

def extract_chapters(html_content):
    """
    提取章节信息和URL
    
    Args:
        html_content (str): HTML内容
        
    Returns:
        dict: 包含章节信息的字典
    """
    if not html_content:
        return None
    
    soup = BeautifulSoup(html_content, 'html.parser')
    
    result = {
        'novel_title': '',
        'total_chapters': 0,
        'chapters': [],
        'navigation': {}
    }
    
    # 提取小说标题
    title_element = soup.find('div', class_='read')
    if title_element:
        # 查找包含标题的文本
        title_text = title_element.find_previous_sibling('div')
        if title_text:
            # 提取标题文本，去除HTML标签
            title_text = title_text.get_text().strip()
            # 提取标题（通常在【】之间）
            if '【' in title_text and '】' in title_text:
                start = title_text.find('【') + 1
                end = title_text.find('】')
                if start > 0 and end > start:
                    result['novel_title'] = title_text[start:end]
    
    # 提取总章节数
    if title_element:
        title_text = title_element.find_previous_sibling('div')
        if title_text:
            text = title_text.get_text()
            if '共' in text and '章' in text:
                try:
                    # 提取数字
                    import re
                    numbers = re.findall(r'\d+', text)
                    if numbers:
                        result['total_chapters'] = int(numbers[-1])  # 取最后一个数字
                except:
                    pass
    
    # 提取章节列表
    chapter_list = soup.find('ul', class_='read')
    if chapter_list:
        chapter_items = chapter_list.find_all('li')
        for item in chapter_items:
            chapter_link = item.find('a')
            if chapter_link:
                chapter_id = item.get('chapter-id', '')
                chapter_title = chapter_link.get_text().strip()
                chapter_url = chapter_link.get('href', '')
                
                # 构建完整URL
                if chapter_url.startswith('/'):
                    chapter_url = 'https://m.shuhaige.net' + chapter_url
                
                result['chapters'].append({
                    'id': chapter_id,
                    'title': chapter_title,
                    'url': chapter_url
                })
    
    # 提取导航信息
    nav_links = soup.find_all('a')
    for link in nav_links:
        href = link.get('href', '')
        text = link.get_text().strip()
        
        if '下一页' in text:
            if href.startswith('/'):
                href = 'https://m.shuhaige.net' + href
            result['navigation']['next_page'] = href
        elif '尾页' in text:
            if href.startswith('/'):
                href = 'https://m.shuhaige.net' + href
            result['navigation']['last_page'] = href
        elif '首页' in text or '首页' in text:
            if href.startswith('/'):
                href = 'https://m.shuhaige.net' + href
            result['navigation']['first_page'] = href
    
    return result

def save_chapters_to_file(data, filename='章节信息.txt'):
    """
    保存章节信息到文件
    
    Args:
        data (dict): 章节数据
        filename (str): 文件名
    """
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"小说标题: {data['novel_title']}\n")
            f.write(f"总章节数: {data['total_chapters']}\n")
            f.write("=" * 50 + "\n\n")
            
            f.write("章节列表:\n")
            for i, chapter in enumerate(data['chapters'], 1):
                f.write(f"{i:3d}. {chapter['title']}\n")
                f.write(f"     ID: {chapter['id']}\n")
                f.write(f"     URL: {chapter['url']}\n")
                f.write("-" * 30 + "\n")
            
            f.write("\n导航链接:\n")
            for nav_type, url in data['navigation'].items():
                f.write(f"{nav_type}: {url}\n")
        
        print(f"章节信息已保存到文件: {filename}")
        
    except Exception as e:
        print(f"保存文件失败: {e}")

def get_all_chapters(base_url):
    """
    获取所有章节（包括分页）
    
    Args:
        base_url (str): 基础URL
        
    Returns:
        list: 所有章节信息
    """
    all_chapters = []
    current_url = base_url
    page_count = 0
    
    while current_url:
        page_count += 1
        print(f"正在获取第 {page_count} 页: {current_url}")
        
        html_content = get_novel_content(current_url)
        if not html_content:
            print(f"获取第 {page_count} 页失败，停止爬取")
            break
        
        chapter_data = extract_chapters(html_content)
        if chapter_data and chapter_data['chapters']:
            all_chapters.extend(chapter_data['chapters'])
            print(f"第 {page_count} 页找到 {len(chapter_data['chapters'])} 个章节")
        else:
            print(f"第 {page_count} 页未找到章节")
        
        # 获取下一页链接
        soup = BeautifulSoup(html_content, 'html.parser')
        next_link = soup.find('a', text='下一页')
        if next_link:
            next_url = next_link.get('href')
            if next_url.startswith('/'):
                next_url = 'https://m.shuhaige.net' + next_url
            current_url = next_url
            print(f"找到下一页链接: {next_url}")
        else:
            print("未找到下一页链接，爬取完成")
            current_url = None
        
        # 添加延迟，避免请求过快
        time.sleep(random.uniform(1, 2))
    
    print(f"总共爬取了 {page_count} 页，获取到 {len(all_chapters)} 个章节")
    return all_chapters

def main():
    """主函数"""
    print("小说爬虫程序")
    print("=" * 50)
    
    # 检查是否有章节URL列表文件
    try:
        chapters = load_chapter_urls_from_file('章节URL列表.txt')
        print(f"读取到 {len(chapters)} 个章节")
        
        if chapters:
            print("前5个章节:")
            for i, chapter in enumerate(chapters[:5], 1):
                print(f"  {i}. {chapter['title']} - {chapter['url']}")
        
        if chapters and chapters[0]['url']:
            print(f"\n从文件加载到 {len(chapters)} 个章节")
            
            # 爬取所有章节内容（实时保存）
            chapters_content = crawl_all_chapters(chapters)
            
            if chapters_content:
                print(f"\n爬取完成！成功爬取 {len(chapters_content)} 个章节")
                print("小说已实时保存到 小说.txt")
            else:
                print("爬取失败，未获取到任何章节内容")
        else:
            print("未找到有效的章节URL，请先运行章节列表爬取功能")
            
    except FileNotFoundError:
        print("未找到章节URL列表文件，请先运行章节列表爬取功能")
    except Exception as e:
        print(f"程序执行出错: {e}")

if __name__ == "__main__":
    main() 